import { Router } from 'express';
import { z } from 'zod';
import { supabaseClient } from '../services/supabase.js';
import { secretsService } from '../services/secretsService.js';
import { loggingService } from '../services/loggingService.js';
import { requireAuth } from '../middleware/auth.js';

const router = Router();

// Request validation schemas
const updateProgressSchema = z.object({
  step: z.enum(['api_keys', 'github_connection', 'sample_loop']),
  completed: z.boolean().default(true)
});

const apiKeysSchema = z.object({
  openai: z.string().optional(),
  anthropic: z.string().optional(),
  github: z.string().optional()
});

// Authentication middleware imported from ../middleware/auth.js

// GET /api/onboarding/progress - Get onboarding progress
router.get('/onboarding/progress', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    
    const { data, error } = await supabaseClient
      .rpc('get_onboarding_progress', { p_user_id: userId });

    if (error) {
      throw new Error(`Failed to get onboarding progress: ${error.message}`);
    }

    await loggingService.log({
      level: 'info',
      message: 'Onboarding progress retrieved',
      service: 'onboarding',
      user_id: userId,
      metadata: {
        progress: data
      }
    });

    res.json({
      success: true,
      progress: data
    });
  } catch (error) {
    console.error('Get onboarding progress error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to get onboarding progress',
      service: 'onboarding',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get onboarding progress',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/onboarding/progress - Update onboarding progress
router.post('/onboarding/progress', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const validatedRequest = updateProgressSchema.parse(req.body);
    
    const { data, error } = await supabaseClient
      .rpc('update_onboarding_progress', {
        p_user_id: userId,
        p_step: validatedRequest.step,
        p_completed: validatedRequest.completed
      });

    if (error) {
      throw new Error(`Failed to update onboarding progress: ${error.message}`);
    }

    await loggingService.log({
      level: 'info',
      message: 'Onboarding progress updated',
      service: 'onboarding',
      user_id: userId,
      metadata: {
        step: validatedRequest.step,
        completed: validatedRequest.completed,
        newProgress: data
      }
    });

    res.json({
      success: true,
      progress: data,
      message: `Step '${validatedRequest.step}' marked as ${validatedRequest.completed ? 'completed' : 'incomplete'}`
    });
  } catch (error) {
    console.error('Update onboarding progress error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    await loggingService.log({
      level: 'error',
      message: 'Failed to update onboarding progress',
      service: 'onboarding',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to update onboarding progress',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/onboarding/api-keys - Save API keys during onboarding
router.post('/onboarding/api-keys', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const validatedRequest = apiKeysSchema.parse(req.body);
    
    let savedKeys = 0;
    const results = [];

    // Save OpenAI key if provided
    if (validatedRequest.openai && validatedRequest.openai.trim()) {
      try {
        await secretsService.storeSecret('openai_api_key', validatedRequest.openai.trim(), 'openai');
        savedKeys++;
        results.push({ provider: 'openai', status: 'saved' });
      } catch (error) {
        results.push({ 
          provider: 'openai', 
          status: 'failed', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    // Save Anthropic key if provided
    if (validatedRequest.anthropic && validatedRequest.anthropic.trim()) {
      try {
        await secretsService.storeSecret('anthropic_api_key', validatedRequest.anthropic.trim(), 'anthropic');
        savedKeys++;
        results.push({ provider: 'anthropic', status: 'saved' });
      } catch (error) {
        results.push({ 
          provider: 'anthropic', 
          status: 'failed', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    // Save GitHub token if provided
    if (validatedRequest.github && validatedRequest.github.trim()) {
      try {
        await secretsService.storeSecret('github_token', validatedRequest.github.trim(), 'github');
        savedKeys++;
        results.push({ provider: 'github', status: 'saved' });
      } catch (error) {
        results.push({ 
          provider: 'github', 
          status: 'failed', 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    if (savedKeys === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid API keys provided',
        message: 'Please provide at least one valid API key'
      });
    }

    await loggingService.log({
      level: 'info',
      message: 'API keys saved during onboarding',
      service: 'onboarding',
      user_id: userId,
      metadata: {
        savedKeys,
        providers: results.filter(r => r.status === 'saved').map(r => r.provider)
      }
    });

    res.json({
      success: true,
      message: `Successfully saved ${savedKeys} API key(s)`,
      results,
      savedCount: savedKeys
    });
  } catch (error) {
    console.error('Save API keys error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    await loggingService.log({
      level: 'error',
      message: 'Failed to save API keys during onboarding',
      service: 'onboarding',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to save API keys',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/onboarding/status - Check if user needs onboarding
router.get('/onboarding/status', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    
    const { data: progress, error } = await supabaseClient
      .rpc('get_onboarding_progress', { p_user_id: userId });

    if (error) {
      throw new Error(`Failed to get onboarding status: ${error.message}`);
    }

    const needsOnboarding = !progress?.completed;
    const currentStep = progress?.current_step || 1;

    res.json({
      success: true,
      needsOnboarding,
      currentStep,
      progress: progress || {
        completed: false,
        current_step: 1,
        steps: {
          api_keys: false,
          github_connection: false,
          sample_loop: false
        }
      }
    });
  } catch (error) {
    console.error('Get onboarding status error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get onboarding status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/onboarding/skip - Skip onboarding (mark as completed)
router.post('/onboarding/skip', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    
    // Mark all steps as completed
    const steps = ['api_keys', 'github_connection', 'sample_loop'];
    
    for (const step of steps) {
      await supabaseClient
        .rpc('update_onboarding_progress', {
          p_user_id: userId,
          p_step: step,
          p_completed: true
        });
    }

    await loggingService.log({
      level: 'info',
      message: 'Onboarding skipped by user',
      service: 'onboarding',
      user_id: userId
    });

    res.json({
      success: true,
      message: 'Onboarding skipped successfully'
    });
  } catch (error) {
    console.error('Skip onboarding error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to skip onboarding',
      service: 'onboarding',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to skip onboarding',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/onboarding/reset - Reset onboarding progress (admin/dev only)
router.post('/onboarding/reset', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    
    // Reset all steps to incomplete
    const { data, error } = await supabaseClient
      .from('settings')
      .update({
        onboarding_progress: {
          completed: false,
          current_step: 1,
          steps: {
            api_keys: false,
            github_connection: false,
            sample_loop: false
          },
          started_at: new Date().toISOString(),
          completed_at: null
        }
      })
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to reset onboarding: ${error.message}`);
    }

    await loggingService.log({
      level: 'info',
      message: 'Onboarding progress reset',
      service: 'onboarding',
      user_id: userId
    });

    res.json({
      success: true,
      message: 'Onboarding progress reset successfully'
    });
  } catch (error) {
    console.error('Reset onboarding error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to reset onboarding',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as onboardingRouter };
