import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';

// Extend Express Request interface to include user data
declare global {
  namespace Express {
    interface Request {
      userId?: string;
      user?: any;
      role?: string;
    }
  }
}

// Initialize Supabase client for server-side authentication
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * JWT Authentication Middleware
 * 
 * Validates JWT tokens using Supabase auth.getUser() and extracts user information.
 * Sets req.userId, req.user, and req.role for downstream middleware and routes.
 * 
 * @param req Express Request object
 * @param res Express Response object  
 * @param next Express NextFunction
 */
export const requireAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Extract Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'Missing Authorization header',
        code: 'MISSING_AUTH_HEADER'
      });
      return;
    }

    // Validate Bearer token format
    if (!authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        error: 'Invalid authentication format',
        message: 'Authorization header must use Bearer token format',
        code: 'INVALID_AUTH_FORMAT'
      });
      return;
    }

    // Extract JWT token
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    if (!token) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'Missing JWT token',
        code: 'MISSING_JWT_TOKEN'
      });
      return;
    }

    // Validate JWT token with Supabase
    const { data, error } = await supabase.auth.getUser(token);

    if (error) {
      // Handle specific Supabase auth errors
      let errorMessage = 'Invalid or expired token';
      let errorCode = 'INVALID_TOKEN';

      if (error.message.includes('expired')) {
        errorMessage = 'Token has expired';
        errorCode = 'EXPIRED_TOKEN';
      } else if (error.message.includes('invalid')) {
        errorMessage = 'Invalid token format or signature';
        errorCode = 'INVALID_TOKEN_FORMAT';
      }

      res.status(401).json({
        error: 'Authentication failed',
        message: errorMessage,
        code: errorCode,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
      return;
    }

    if (!data.user) {
      res.status(401).json({
        error: 'Authentication failed',
        message: 'User not found or token invalid',
        code: 'USER_NOT_FOUND'
      });
      return;
    }

    // Extract user information from validated token
    req.userId = data.user.id;
    req.user = data.user;
    
    // Extract role from user metadata or app metadata
    // Priority: app_metadata.role > user_metadata.role > 'authenticated' (default)
    req.role = data.user.app_metadata?.role || 
               data.user.user_metadata?.role || 
               'authenticated';

    // Log successful authentication in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Auth: User ${req.userId} authenticated with role: ${req.role}`);
    }

    next();
  } catch (error) {
    console.error('❌ Auth middleware error:', error);
    
    res.status(500).json({
      error: 'Authentication service error',
      message: 'Internal server error during authentication',
      code: 'AUTH_SERVICE_ERROR',
      details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Admin Role Authorization Middleware
 * 
 * Requires user to be authenticated AND have admin role.
 * Must be used after requireAuth middleware.
 * 
 * @param req Express Request object
 * @param res Express Response object
 * @param next Express NextFunction
 */
export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  // Check if user is authenticated (requireAuth should run first)
  if (!req.userId || !req.role) {
    res.status(401).json({
      error: 'Authentication required',
      message: 'User must be authenticated before checking admin role',
      code: 'UNAUTHENTICATED'
    });
    return;
  }

  // Check admin role
  if (req.role !== 'admin') {
    res.status(403).json({
      error: 'Insufficient permissions',
      message: 'Admin role required for this operation',
      code: 'INSUFFICIENT_PERMISSIONS',
      required_role: 'admin',
      user_role: req.role
    });
    return;
  }

  // Log admin access in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔑 Admin: User ${req.userId} accessing admin endpoint`);
  }

  next();
};

/**
 * Optional Authentication Middleware
 * 
 * Attempts to authenticate user but doesn't fail if no token provided.
 * Useful for endpoints that work for both authenticated and anonymous users.
 * 
 * @param req Express Request object
 * @param res Express Response object
 * @param next Express NextFunction
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    // If no auth header, continue without authentication
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      next();
      return;
    }

    const token = authHeader.substring(7);
    
    if (!token) {
      next();
      return;
    }

    // Try to validate token, but don't fail if invalid
    const { data, error } = await supabase.auth.getUser(token);

    if (!error && data.user) {
      req.userId = data.user.id;
      req.user = data.user;
      req.role = data.user.app_metadata?.role || 
                 data.user.user_metadata?.role || 
                 'authenticated';
    }

    next();
  } catch (error) {
    // Log error but continue without authentication
    console.warn('⚠️ Optional auth error:', error);
    next();
  }
};

/**
 * Service Role Authorization Middleware
 * 
 * Validates service role key for internal API calls.
 * Used for admin operations that bypass user authentication.
 * 
 * @param req Express Request object
 * @param res Express Response object
 * @param next Express NextFunction
 */
export const requireServiceRole = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        error: 'Service authentication required',
        message: 'Missing or invalid service role authorization',
        code: 'MISSING_SERVICE_AUTH'
      });
      return;
    }

    const token = authHeader.substring(7);
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!serviceRoleKey) {
      res.status(500).json({
        error: 'Service configuration error',
        message: 'Service role key not configured',
        code: 'SERVICE_CONFIG_ERROR'
      });
      return;
    }

    if (token !== serviceRoleKey) {
      res.status(403).json({
        error: 'Invalid service credentials',
        message: 'Service role key does not match',
        code: 'INVALID_SERVICE_KEY'
      });
      return;
    }

    // Set service role context
    req.role = 'service_role';
    req.userId = 'service';

    next();
  } catch (error) {
    console.error('❌ Service role auth error:', error);
    
    res.status(500).json({
      error: 'Service authentication error',
      message: 'Internal error during service authentication',
      code: 'SERVICE_AUTH_ERROR'
    });
  }
};

/**
 * Combined Authentication Middleware
 * 
 * Accepts either user JWT or service role key.
 * Useful for endpoints that can be accessed by both users and services.
 * 
 * @param req Express Request object
 * @param res Express Response object
 * @param next Express NextFunction
 */
export const requireAuthOrService = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        error: 'Authentication required',
        message: 'Missing Authorization header',
        code: 'MISSING_AUTH_HEADER'
      });
      return;
    }

    const token = authHeader.substring(7);
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    // Check if it's a service role key
    if (serviceRoleKey && token === serviceRoleKey) {
      req.role = 'service_role';
      req.userId = 'service';
      next();
      return;
    }

    // Otherwise, validate as user JWT
    const { data, error } = await supabase.auth.getUser(token);

    if (error || !data.user) {
      res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid user token or service key',
        code: 'INVALID_CREDENTIALS'
      });
      return;
    }

    req.userId = data.user.id;
    req.user = data.user;
    req.role = data.user.app_metadata?.role || 
               data.user.user_metadata?.role || 
               'authenticated';

    next();
  } catch (error) {
    console.error('❌ Combined auth error:', error);
    
    res.status(500).json({
      error: 'Authentication service error',
      message: 'Internal server error during authentication',
      code: 'AUTH_SERVICE_ERROR'
    });
  }
};

export default {
  requireAuth,
  requireAdmin,
  optionalAuth,
  requireServiceRole,
  requireAuthOrService
};
