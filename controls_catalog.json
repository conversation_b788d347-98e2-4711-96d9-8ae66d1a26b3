{"metadata": {"created": "2025-06-19", "purpose": "Comprehensive catalog of all interactive controls across Metamorphic Reactor application", "viewports": {"desktop": "1280x800", "tablet": "768x1024", "mobile": "390x844"}}, "pages": {"/": {"title": "<PERSON>", "url": "http://localhost:8080/", "controls": [{"id": "skip-to-main", "type": "link", "ref": "s1e8", "text": "Skip to main content", "action": "navigate", "target": "#main-content", "accessibility": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "get-started-header", "type": "button", "ref": "s1e14", "text": "Get Started", "description": "Navigate to dashboard to get started", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "launch-dashboard", "type": "button", "ref": "s1e22", "text": "Launch Dashboard", "description": "Launch dashboard to start using the AI code assistant", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "view-github", "type": "button", "ref": "s1e23", "text": "View on GitHub", "description": "View source code on GitHub", "action": "external_link", "target": "github", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "start-optimizing", "type": "button", "ref": "s1e79", "text": "Start Optimizing Now", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}]}, "/dashboard": {"title": "Dashboard", "url": "http://localhost:8080/dashboard", "controls": [{"id": "back-button", "type": "button", "ref": "s2e11", "text": "Back", "action": "navigate", "target": "previous", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "learn-button", "type": "button", "ref": "s2e20", "text": "Learn", "action": "unknown", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "monitor-button", "type": "button", "ref": "s2e23", "text": "Monitor", "action": "navigate", "target": "/monitoring", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "file-button", "type": "button", "ref": "s2e26", "text": "File", "action": "unknown", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "theme-toggle", "type": "button", "ref": "s2e30", "text": "🌙 Toggle theme Dark", "action": "toggle_theme", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "breadcrumb-home", "type": "link", "ref": "s2e38", "text": "Home", "action": "navigate", "target": "/", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "setup-wizard", "type": "button", "ref": "s2e52", "text": "Setup Wizard", "action": "open_wizard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "manual-setup", "type": "button", "ref": "s2e55", "text": "Manual Setup", "action": "open_manual_setup", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "enhanced-button", "type": "button", "ref": "s2e84", "text": "Enhanced", "action": "toggle_mode", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "run-loop", "type": "button", "ref": "s2e86", "text": "Run Loop", "action": "execute_reactor", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "code-editor", "type": "textbox", "ref": "s2e96", "text": "Code input editor", "action": "input", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "connect-github", "type": "button", "ref": "s2e107", "text": "Connect GitHub", "action": "oauth_connect", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "stream-tab", "type": "tab", "ref": "s2e123", "text": "Stream", "action": "switch_tab", "selected": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "logs-tab", "type": "tab", "ref": "s2e124", "text": "Logs", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "start-stream", "type": "button", "ref": "s2e131", "text": "Start", "action": "start_stream", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "clear-stream", "type": "button", "ref": "s2e134", "text": "Clear", "action": "clear_stream", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}]}, "/monitoring": {"title": "Monitoring Dashboard", "url": "http://localhost:8080/monitoring", "controls": [{"id": "back-to-dashboard", "type": "button", "ref": "s1e11", "text": "Back to Dashboard", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "overview-tab-main", "type": "tab", "ref": "s1e29", "text": "Overview", "action": "switch_tab", "selected": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "health-tab-main", "type": "tab", "ref": "s1e36", "text": "Health", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "billing-tab-main", "type": "tab", "ref": "s1e40", "text": "Billing", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "logs-tab-main", "type": "tab", "ref": "s1e45", "text": "Logs", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "queue-tab-main", "type": "tab", "ref": "s1e53", "text": "Queue", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "secrets-tab-main", "type": "tab", "ref": "s1e57", "text": "Secrets", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "admin-tab-main", "type": "tab", "ref": "s1e61", "text": "Admin", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "code-editor-button", "type": "button", "ref": "s1e147", "text": "Code Editor", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "overview-tab-dashboard", "type": "tab", "ref": "s1e153", "text": "Overview", "action": "switch_tab", "selected": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "repositories-tab", "type": "tab", "ref": "s1e159", "text": "Repositories", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "analytics-tab", "type": "tab", "ref": "s1e166", "text": "Analytics", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "admin-tab-dashboard", "type": "tab", "ref": "s1e173", "text": "Admin", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "workspace-tab", "type": "tab", "ref": "s1e177", "text": "Workspace", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "alerts-tab", "type": "tab", "ref": "s1e182", "text": "<PERSON><PERSON><PERSON>", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "temperature-slider", "type": "slider", "ref": "s1e439", "text": "Temperature", "action": "adjust_value", "value": "0.70", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-model-select", "type": "combobox", "ref": "s1e408", "text": "Planner Model", "action": "select_option", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}]}, "/settings": {"title": "Enhanced <PERSON>s", "url": "http://localhost:8080/settings", "controls": [{"id": "dashboard-button", "type": "button", "ref": "s1e11", "text": "Dashboard", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "providers-tab", "type": "tab", "ref": "s1e31", "text": "Providers", "action": "switch_tab", "selected": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "cost-tokens-tab", "type": "tab", "ref": "s1e43", "text": "Cost & Tokens", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "reliability-tab", "type": "tab", "ref": "s1e47", "text": "Reliability", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "monitoring-tab", "type": "tab", "ref": "s1e50", "text": "Monitoring", "action": "switch_tab", "selected": false, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-provider-select", "type": "combobox", "ref": "s1e77", "text": "🤖 OpenAI", "action": "select_option", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-model-select-settings", "type": "combobox", "ref": "s1e81", "text": "gpt-4o", "action": "select_option", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-api-key", "type": "textbox", "ref": "s1e87", "text": "Enter openai API key...", "action": "input", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-test-button", "type": "button", "ref": "s1e92", "text": "Test", "action": "test_connection", "disabled": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-temperature-slider", "type": "slider", "ref": "s1e105", "text": "Temperature", "action": "adjust_value", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-max-tokens", "type": "spinbutton", "ref": "s1e112", "text": "<PERSON>", "action": "input_number", "value": "2000", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "planner-advanced-switch", "type": "switch", "ref": "s1e115", "text": "Advanced Settings", "action": "toggle", "checked": true, "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "export-button", "type": "button", "ref": "s1e207", "text": "Export", "action": "export_config", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "import-button", "type": "button", "ref": "s1e214", "text": "Import", "action": "import_config", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "save-settings-button", "type": "button", "ref": "s1e219", "text": "Save Settings", "action": "save_settings", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}]}, "/history": {"title": "Transformation History", "url": "http://localhost:8080/history", "controls": [{"id": "dashboard-button-history", "type": "button", "ref": "s1e19", "text": "Dashboard", "action": "navigate", "target": "/dashboard", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "search-transformations", "type": "textbox", "ref": "s1e31", "text": "Search transformations...", "action": "search_input", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "status-filter", "type": "combobox", "ref": "s1e32", "text": "All Status", "action": "select_option", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}, {"id": "sort-filter", "type": "combobox", "ref": "s1e36", "text": "Newest First", "action": "select_option", "viewport_visibility": {"desktop": true, "tablet": true, "mobile": true}}]}}}