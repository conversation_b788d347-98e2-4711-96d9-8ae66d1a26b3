name: <PERSON>rowser MCP Comprehensive Audit

on:
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/web/**'
      - 'src/**'
      - 'components/**'
      - 'pages/**'
      - '.github/workflows/ci-browser-mcp.yml'
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  AUDIT_TIMEOUT: '300000'
  HEADLESS: 'true'

jobs:
  browser-mcp-audit:
    name: 🌐 Browser MCP UI Audit
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 🔧 Install dependencies
        run: |
          npm ci
          npm run build
          
      - name: 🚀 Start application
        run: |
          npm run dev &
          sleep 10
          curl --retry 10 --retry-delay 3 --retry-connrefused http://localhost:8080/
        env:
          CI: true
          
      - name: 🌐 Install Browser MCP dependencies
        run: |
          npm install -g @playwright/test
          npx playwright install chromium
          
      - name: 🔍 Run Browser MCP Audit
        run: |
          node browser_mcp_audit.js
        env:
          HEADLESS: true
          VIEWPORT_DESKTOP: '1280x800'
          VIEWPORT_TABLET: '768x1024'
          VIEWPORT_MOBILE: '390x844'
          AUDIT_TIMEOUT: ${{ env.AUDIT_TIMEOUT }}
          
      - name: 📊 Validate audit results
        run: |
          # Check if controls catalog exists and has expected structure
          if [ ! -f "controls_catalog.json" ]; then
            echo "❌ Controls catalog not found"
            exit 1
          fi
          
          # Check if full log exists
          if [ ! -f "controls_full_log.json" ]; then
            echo "❌ Full audit log not found"
            exit 1
          fi
          
          # Check if HTML report exists
          if [ ! -f "click_trail_report.html" ]; then
            echo "❌ HTML report not found"
            exit 1
          fi
          
          # Validate JSON structure
          node -e "
            const catalog = require('./controls_catalog.json');
            const log = require('./controls_full_log.json');
            
            console.log('📋 Audit Summary:');
            console.log('  Pages discovered:', Object.keys(catalog.pages).length);
            console.log('  Total controls:', log.summary.totalControls);
            console.log('  Success rate:', log.summary.successRate);
            console.log('  Interactions logged:', log.interactions.length);
            
            // Fail if no controls discovered
            if (log.summary.totalControls === 0) {
              console.error('❌ No controls discovered');
              process.exit(1);
            }
            
            // Fail if success rate below 90%
            const successRate = parseFloat(log.summary.successRate);
            if (successRate < 90) {
              console.error('❌ Success rate below 90%:', successRate + '%');
              process.exit(1);
            }
            
            // Check for new controls not in baseline
            const expectedPages = ['/', '/dashboard', '/monitoring', '/settings', '/history'];
            const discoveredPages = Object.keys(catalog.pages);
            
            for (const page of expectedPages) {
              if (!discoveredPages.includes(page)) {
                console.error('❌ Missing expected page:', page);
                process.exit(1);
              }
            }
            
            console.log('✅ All validation checks passed');
          "
          
      - name: 🔒 Check accessibility violations
        run: |
          node -e "
            const log = require('./controls_full_log.json');
            const violations = log.violations || [];
            
            if (violations.length > 0) {
              console.error('❌ Accessibility violations found:', violations.length);
              violations.forEach(v => console.error('  -', v.description));
              process.exit(1);
            }
            
            console.log('✅ No accessibility violations found');
          "
          
      - name: 📈 Check for new controls
        if: github.event_name == 'pull_request'
        run: |
          # Download baseline from main branch
          curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/contents/controls_catalog.json?ref=main" \
            | jq -r '.content' | base64 -d > baseline_controls.json || echo "{}" > baseline_controls.json
            
          node -e "
            const baseline = require('./baseline_controls.json');
            const current = require('./controls_catalog.json');
            
            const baselineCount = baseline.pages ? 
              Object.values(baseline.pages).reduce((sum, page) => sum + (page.controls?.length || 0), 0) : 0;
            const currentCount = Object.values(current.pages).reduce((sum, page) => sum + page.controls.length, 0);
            
            console.log('📊 Control count comparison:');
            console.log('  Baseline:', baselineCount);
            console.log('  Current:', currentCount);
            console.log('  Difference:', currentCount - baselineCount);
            
            if (currentCount > baselineCount) {
              console.log('🆕 New controls detected - ensure they are tested');
            }
          "
          
      - name: 📤 Upload audit artifacts
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: browser-mcp-audit-results
          path: |
            controls_catalog.json
            controls_full_log.json
            click_trail_report.html
            screenshots/
          retention-days: 30
          
      - name: 💬 Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            try {
              const log = JSON.parse(fs.readFileSync('controls_full_log.json', 'utf8'));
              const catalog = JSON.parse(fs.readFileSync('controls_catalog.json', 'utf8'));
              
              const summary = `
              ## 🌐 Browser MCP Audit Results
              
              | Metric | Value |
              |--------|-------|
              | 📄 Pages Audited | ${Object.keys(catalog.pages).length} |
              | 🎛️ Controls Discovered | ${log.summary.totalControls} |
              | ✅ Success Rate | ${log.summary.successRate} |
              | 🖼️ Screenshots | ${log.screenshots.length} |
              | ⚠️ Violations | ${log.violations.length} |
              
              ### 📋 Controls by Page
              ${Object.entries(catalog.pages).map(([path, page]) => 
                `- **${page.title}** (${path}): ${page.controls.length} controls`
              ).join('\n')}
              
              ### 🎯 Control Types
              ${Object.entries(log.summary.controlTypes || {}).map(([type, count]) => 
                `- ${type}: ${count}`
              ).join('\n')}
              
              [📊 View Full Report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: summary
              });
            } catch (error) {
              console.error('Failed to post comment:', error);
            }
            
      - name: 🎯 Set status check
        if: always()
        run: |
          if [ -f "controls_full_log.json" ]; then
            SUCCESS_RATE=$(node -e "console.log(require('./controls_full_log.json').summary.successRate)")
            echo "Browser MCP Audit completed with $SUCCESS_RATE success rate"
          else
            echo "❌ Browser MCP Audit failed - no results generated"
            exit 1
          fi
