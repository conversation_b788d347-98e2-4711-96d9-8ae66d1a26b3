import { Router } from 'express';
import { z } from 'zod';
import <PERSON><PERSON> from 'stripe';
import { billingService } from '../services/billingService.js';
import { loggingService } from '../services/loggingService.js';
import { requireAuth } from '../middleware/auth.js';

const router = Router();

// Initialize Stripe for webhook verification
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20'
});

// Request validation schemas
const createSubscriptionSchema = z.object({
  price_id: z.string().min(1, 'Price ID is required'),
  trial_days: z.number().min(0).max(30).optional()
});

const recordUsageSchema = z.object({
  usage_type: z.string().min(1, 'Usage type is required'),
  quantity: z.number().min(1).default(1),
  session_id: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

// Authentication middleware imported from ../middleware/auth.js

// GET /api/billing/plans - Get available subscription plans
router.get('/billing/plans', async (req, res) => {
  try {
    const plans = await billingService.getSubscriptionPlans();
    
    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Get billing plans error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription plans',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/billing/subscription - Get user's current subscription
router.get('/billing/subscription', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const subscription = await billingService.getUserSubscription(userId);
    
    res.json({
      success: true,
      data: subscription
    });
  } catch (error) {
    console.error('Get subscription error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to get user subscription',
      service: 'billing',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get subscription',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/billing/create-subscription - Create a new subscription
router.post('/billing/create-subscription', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const validatedRequest = createSubscriptionSchema.parse(req.body);
    
    // Check if user already has an active subscription
    const existingSubscription = await billingService.getUserSubscription(userId);
    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        error: 'User already has an active subscription'
      });
    }

    // Create or get Stripe customer
    const userEmail = req.body.email || `user-${userId}@example.com`; // Get from auth token in real app
    const customerId = await billingService.createCustomer(userId, userEmail);

    // Create subscription
    const subscription = await billingService.createSubscription(
      userId,
      customerId,
      validatedRequest.price_id,
      validatedRequest.trial_days
    );

    res.json({
      success: true,
      data: subscription,
      message: 'Subscription created successfully'
    });
  } catch (error) {
    console.error('Create subscription error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    await loggingService.log({
      level: 'error',
      message: 'Failed to create subscription',
      service: 'billing',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to create subscription',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/billing/usage - Get usage summary for current billing period
router.get('/billing/usage', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const usageSummary = await billingService.getUsageSummary(userId);
    
    res.json({
      success: true,
      data: usageSummary
    });
  } catch (error) {
    console.error('Get usage summary error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to get usage summary',
      service: 'billing',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get usage summary',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/billing/record-usage - Record usage (internal API)
router.post('/billing/record-usage', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const validatedRequest = recordUsageSchema.parse(req.body);
    
    await billingService.recordUsage(
      userId,
      validatedRequest.usage_type,
      validatedRequest.quantity,
      validatedRequest.session_id,
      validatedRequest.metadata
    );

    res.json({
      success: true,
      message: 'Usage recorded successfully'
    });
  } catch (error) {
    console.error('Record usage error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    await loggingService.log({
      level: 'error',
      message: 'Failed to record usage',
      service: 'billing',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to record usage',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/billing/usage-limit - Check usage limits
router.get('/billing/usage-limit', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const usageType = req.query.type as string || 'transformation';
    
    const limitCheck = await billingService.checkUsageLimit(userId, usageType);
    
    res.json({
      success: true,
      data: limitCheck
    });
  } catch (error) {
    console.error('Check usage limit error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to check usage limit',
      service: 'billing',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to check usage limit',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/billing/create-checkout-session - Create Stripe checkout session
router.post('/billing/create-checkout-session', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const { price_id, success_url, cancel_url } = req.body;
    
    if (!price_id || !success_url || !cancel_url) {
      return res.status(400).json({
        success: false,
        error: 'price_id, success_url, and cancel_url are required'
      });
    }

    // Create or get Stripe customer
    const userEmail = req.body.email || `user-${userId}@example.com`;
    const customerId = await billingService.createCustomer(userId, userEmail);

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: success_url,
      cancel_url: cancel_url,
      metadata: {
        user_id: userId
      }
    });

    await loggingService.log({
      level: 'info',
      message: 'Checkout session created',
      service: 'billing',
      user_id: userId,
      metadata: {
        session_id: session.id,
        price_id
      }
    });

    res.json({
      success: true,
      data: {
        checkout_url: session.url,
        session_id: session.id
      }
    });
  } catch (error) {
    console.error('Create checkout session error:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Failed to create checkout session',
      service: 'billing',
      user_id: req.userId,
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to create checkout session',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/billing/webhook - Stripe webhook endpoint
router.post('/billing/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'] as string;
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!endpointSecret) {
    console.error('STRIPE_WEBHOOK_SECRET not configured');
    return res.status(500).json({ error: 'Webhook secret not configured' });
  }

  let event: Stripe.Event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return res.status(400).json({ error: 'Invalid signature' });
  }

  try {
    // Handle the event
    await billingService.handleWebhook(event);
    
    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handling failed:', error);
    
    await loggingService.log({
      level: 'error',
      message: 'Webhook handling failed',
      service: 'billing',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        event_type: event.type,
        event_id: event.id
      }
    });

    res.status(500).json({ error: 'Webhook handling failed' });
  }
});

// GET /api/billing/detailed - Get detailed cost breakdown
router.get('/billing/detailed', requireAuth, async (req, res) => {
  try {
    const userId = req.userId;
    const { timeframe = '30d' } = req.query;

    // Get detailed usage breakdown
    const usage = await billingService.getUsageData(userId, {
      timeframe: timeframe as string,
      includeBreakdown: true
    });

    // Get cost predictions
    const predictions = await billingService.getCostPredictions(userId);

    // Get recent transactions
    const transactions = await billingService.getRecentTransactions(userId, 10);

    res.json({
      success: true,
      data: {
        usage,
        predictions,
        transactions,
        breakdown: {
          byService: usage.serviceBreakdown || {},
          byTimeframe: usage.timeframeBreakdown || {},
          topCostDrivers: usage.topCostDrivers || []
        }
      }
    });

  } catch (error) {
    console.error('Get detailed billing error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get detailed billing information'
    });
  }
});

// GET /api/billing/health - Billing service health check
router.get('/billing/health', async (req, res) => {
  try {
    // Check if we can connect to Stripe
    const plans = await billingService.getSubscriptionPlans();
    const isHealthy = Array.isArray(plans);

    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        stripe_connection: true,
        database_connection: isHealthy,
        plans_available: plans.length > 0
      },
      data: isHealthy ? {
        available_plans: plans.length
      } : null
    });
  } catch (error) {
    console.error('Billing health check error:', error);
    
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as billingRouter };
