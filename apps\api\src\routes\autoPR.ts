import { Router } from 'express';
import { autoPRService } from '../services/autoPRService.js';
import { loggingService } from '../services/loggingService.js';
import { supabaseClient } from '../services/supabase.js';
import { requireAuth } from '../middleware/auth.js';

const router = Router();

/**
 * POST /api/auto-pr/create
 * Create an automatic PR for an approved improvement
 */
router.post('/auto-pr/create', requireAuth, async (req, res) => {
  try {
    const user = req.user;

    const {
      repositoryId,
      planId,
      opportunityId,
      patch,
      branchName,
      title,
      description,
      isDraft = true
    } = req.body;

    // Validate required fields
    if (!repositoryId || !planId || !opportunityId || !patch) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'repositoryId, planId, opportunityId, and patch are required'
      });
    }

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('*')
      .eq('id', repositoryId)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found or access denied' });
    }

    // Verify improvement plan ownership
    const { data: plan, error: planError } = await supabaseClient
      .from('improvement_plans')
      .select('*')
      .eq('id', planId)
      .eq('repository_id', repositoryId)
      .single();

    if (planError || !plan) {
      return res.status(404).json({ error: 'Improvement plan not found' });
    }

    // Create the PR
    const result = await autoPRService.createAutomaticPR({
      repositoryId,
      planId,
      opportunityId,
      patch,
      branchName,
      title,
      description,
      isDraft
    });

    if (result.success) {
      await loggingService.log({
        level: 'info',
        message: 'Manual PR creation successful',
        service: 'auto-pr-api',
        metadata: {
          userId: user.id,
          repositoryId,
          planId,
          prNumber: result.prNumber,
          prUrl: result.prUrl,
          cost: result.cost
        }
      });

      res.json({
        success: true,
        prNumber: result.prNumber,
        prUrl: result.prUrl,
        branchName: result.branchName,
        cost: result.cost
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        cost: result.cost
      });
    }

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to create automatic PR',
      service: 'auto-pr-api',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'POST /auto-pr/create',
        body: req.body
      }
    });

    res.status(500).json({
      error: 'Failed to create automatic PR',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/auto-pr/active
 * Get active PRs created by the auto-PR service
 */
router.get('/auto-pr/active', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Get active PRs for user's repositories
    const { data: userRepos, error: reposError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('user_id', user.id);

    if (reposError) {
      throw reposError;
    }

    const userRepoIds = userRepos?.map(repo => repo.id) || [];
    const activePRs = autoPRService.getActivePRs()
      .filter(pr => userRepoIds.includes(pr.repositoryId));

    // Get additional PR details from database
    const prDetails = await Promise.all(
      activePRs.map(async (pr) => {
        const { data: planData } = await supabaseClient
          .from('improvement_plans')
          .select(`
            *,
            improvement_opportunities!inner(*),
            autonomous_repositories!inner(owner, name)
          `)
          .eq('id', pr.planId)
          .single();

        return {
          ...pr,
          planData: planData || null
        };
      })
    );

    res.json({
      activePRs: prDetails
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get active PRs',
      service: 'auto-pr-api',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /auto-pr/active'
      }
    });

    res.status(500).json({
      error: 'Failed to get active PRs',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/auto-pr/plan/:planId
 * Get PR information for a specific plan
 */
router.get('/auto-pr/plan/:planId', async (req, res) => {
  try {
    const { planId } = req.params;
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // Get user from Supabase auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Verify plan ownership
    const { data: plan, error: planError } = await supabaseClient
      .from('improvement_plans')
      .select(`
        *,
        autonomous_repositories!inner(user_id)
      `)
      .eq('id', planId)
      .single();

    if (planError || !plan || plan.autonomous_repositories.user_id !== user.id) {
      return res.status(404).json({ error: 'Plan not found or access denied' });
    }

    // Get PR info
    const prInfo = autoPRService.getPRByPlanId(planId);

    res.json({
      planId,
      prInfo: prInfo || null,
      planStatus: plan.status,
      executionResult: plan.execution_result
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get PR info for plan',
      service: 'auto-pr-api',
      metadata: {
        planId: req.params.planId,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /auto-pr/plan/:planId'
      }
    });

    res.status(500).json({
      error: 'Failed to get PR info',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/auto-pr/cleanup
 * Clean up old PR tracking data
 */
router.post('/auto-pr/cleanup', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // This endpoint could be restricted to admin users in production
    autoPRService.cleanupOldPRs();

    await loggingService.log({
      level: 'info',
      message: 'PR cleanup completed',
      service: 'auto-pr-api',
      metadata: {
        endpoint: 'POST /auto-pr/cleanup'
      }
    });

    res.json({
      message: 'PR cleanup completed successfully'
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to cleanup PRs',
      service: 'auto-pr-api',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'POST /auto-pr/cleanup'
      }
    });

    res.status(500).json({
      error: 'Failed to cleanup PRs',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as autoPRRouter };
